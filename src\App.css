/* AdminLTE v4 React Customizations */

/* User Image Placeholders */
.user-image-wrapper {
  display: inline-block;
}

.user-image-placeholder {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  margin-right: 8px;
}

.user-image-placeholder.large {
  width: 80px;
  height: 80px;
  font-size: 24px;
  margin: 0 auto 10px;
}

/* Brand Image Placeholder */
.brand-image-placeholder {
  width: 33px;
  height: 33px;
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
  margin-right: 10px;
  opacity: 0.9;
}

/* Custom AdminLTE Overrides */
.app-wrapper {
  min-height: 100vh;
}

/* Sidebar customizations */
.app-sidebar .nav-link {
  transition: all 0.3s ease;
}

.app-sidebar .nav-link:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateX(5px);
}

.app-sidebar .nav-link.active {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  color: white;
  border-radius: 8px;
  margin: 2px 8px;
}

/* Content area */
.app-content {
  padding: 20px 0;
}

/* Card enhancements */
.card {
  border: none;
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-header {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1px solid #dee2e6;
  border-radius: 12px 12px 0 0 !important;
}

/* Small box widgets */
.small-box {
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease;
}

.small-box:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.small-box .icon {
  transition: all 0.3s ease;
}

.small-box:hover .icon {
  transform: scale(1.1);
}

/* Info box widgets */
.info-box {
  border-radius: 12px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.info-box:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.info-box-icon {
  border-radius: 12px 0 0 12px;
}

/* Table enhancements */
.table {
  border-radius: 12px;
  overflow: hidden;
}

.table thead th {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border: none;
  font-weight: 600;
  text-transform: uppercase;
  font-size: 12px;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: all 0.2s ease;
}

.table tbody tr:hover {
  background-color: rgba(0, 123, 255, 0.05);
  transform: scale(1.01);
}

/* Button enhancements */
.btn {
  border-radius: 8px;
  font-weight: 500;
  transition: all 0.3s ease;
  border: none;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
  box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
}

.btn-success {
  background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%);
  box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #dc3545 0%, #bd2130 100%);
  box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.btn-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
  box-shadow: 0 2px 4px rgba(255, 193, 7, 0.3);
  color: #212529;
}

/* Form enhancements */
.form-control {
  border-radius: 8px;
  border: 1px solid #ced4da;
  transition: all 0.3s ease;
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
  transform: translateY(-1px);
}

/* Modal enhancements */
.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.modal-header {
  border-bottom: 1px solid #dee2e6;
  border-radius: 12px 12px 0 0;
}

.modal-footer {
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 12px 12px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .user-image-placeholder {
    width: 28px;
    height: 28px;
    font-size: 10px;
  }

  .brand-image-placeholder {
    width: 28px;
    height: 28px;
    font-size: 14px;
  }
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.slide-in {
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(-100%);
  }
  to {
    transform: translateX(0);
  }
}
