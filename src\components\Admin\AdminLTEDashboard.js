import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import AdminController from '../../controllers/AdminController';
import AdminLTELayout from '../Layout/AdminLTELayout';

const AdminLTEDashboard = () => {
  const [stats, setStats] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  
  const adminController = new AdminController();

  useEffect(() => {
    loadDashboardStats();
  }, []);

  const loadDashboardStats = async () => {
    try {
      setLoading(true);
      const result = adminController.getDashboardStats();
      
      if (result.success) {
        setStats(result.data);
        setError(null);
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error loading dashboard stats:', err);
      setError('Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <AdminLTELayout>
        <div className="d-flex justify-content-center align-items-center" style={{height: '400px'}}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </AdminLTELayout>
    );
  }

  if (error) {
    return (
      <AdminLTELayout>
        <div className="alert alert-danger" role="alert">
          <h4 className="alert-heading">Error!</h4>
          <p>{error}</p>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <div className="fade-in">
        {/* Stats Row */}
        <div className="row">
          {/* Total Users */}
          <div className="col-lg-3 col-6">
            <div className="small-box text-bg-primary">
              <div className="inner">
                <h3>{stats?.totalUsers || 0}</h3>
                <p>Total Users</p>
              </div>
              <div className="small-box-icon">
                <i className="bi bi-people-fill"></i>
              </div>
              <Link to="/admin/users" className="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover">
                More info <i className="bi bi-link-45deg"></i>
              </Link>
            </div>
          </div>

          {/* Active Users */}
          <div className="col-lg-3 col-6">
            <div className="small-box text-bg-success">
              <div className="inner">
                <h3>{stats?.activeUsers || 0}</h3>
                <p>Active Users</p>
              </div>
              <div className="small-box-icon">
                <i className="bi bi-person-check-fill"></i>
              </div>
              <Link to="/admin/users" className="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover">
                More info <i className="bi bi-link-45deg"></i>
              </Link>
            </div>
          </div>

          {/* Admin Users */}
          <div className="col-lg-3 col-6">
            <div className="small-box text-bg-warning">
              <div className="inner">
                <h3>{stats?.adminUsers || 0}</h3>
                <p>Admin Users</p>
              </div>
              <div className="small-box-icon">
                <i className="bi bi-shield-fill-check"></i>
              </div>
              <Link to="/admin/users" className="small-box-footer link-dark link-underline-opacity-0 link-underline-opacity-50-hover">
                More info <i className="bi bi-link-45deg"></i>
              </Link>
            </div>
          </div>

          {/* Active Sessions */}
          <div className="col-lg-3 col-6">
            <div className="small-box text-bg-danger">
              <div className="inner">
                <h3>{stats?.activeSessions || 0}</h3>
                <p>Active Sessions</p>
              </div>
              <div className="small-box-icon">
                <i className="bi bi-activity"></i>
              </div>
              <a href="#" className="small-box-footer link-light link-underline-opacity-0 link-underline-opacity-50-hover">
                More info <i className="bi bi-link-45deg"></i>
              </a>
            </div>
          </div>
        </div>

        {/* Info Boxes Row */}
        <div className="row">
          <div className="col-12 col-sm-6 col-md-3">
            <div className="info-box">
              <span className="info-box-icon text-bg-info shadow-sm">
                <i className="bi bi-gear-fill"></i>
              </span>
              <div className="info-box-content">
                <span className="info-box-text">System Status</span>
                <span className="info-box-number">Online</span>
              </div>
            </div>
          </div>

          <div className="col-12 col-sm-6 col-md-3">
            <div className="info-box mb-3">
              <span className="info-box-icon text-bg-danger shadow-sm">
                <i className="bi bi-heart-fill"></i>
              </span>
              <div className="info-box-content">
                <span className="info-box-text">Server Health</span>
                <span className="info-box-number">98%</span>
              </div>
            </div>
          </div>

          <div className="col-12 col-sm-6 col-md-3">
            <div className="info-box mb-3">
              <span className="info-box-icon text-bg-success shadow-sm">
                <i className="bi bi-database-fill"></i>
              </span>
              <div className="info-box-content">
                <span className="info-box-text">Database</span>
                <span className="info-box-number">Connected</span>
              </div>
            </div>
          </div>

          <div className="col-12 col-sm-6 col-md-3">
            <div className="info-box mb-3">
              <span className="info-box-icon text-bg-warning shadow-sm">
                <i className="bi bi-cpu-fill"></i>
              </span>
              <div className="info-box-content">
                <span className="info-box-text">CPU Usage</span>
                <span className="info-box-number">45%</span>
              </div>
            </div>
          </div>
        </div>

        {/* Recent Users Card */}
        <div className="row">
          <div className="col-md-12">
            <div className="card">
              <div className="card-header">
                <h3 className="card-title">
                  <i className="bi bi-people-fill me-1"></i>
                  Recent Users
                </h3>
                <div className="card-tools">
                  <Link to="/admin/users" className="btn btn-primary btn-sm">
                    <i className="bi bi-plus-circle me-1"></i>
                    Add User
                  </Link>
                </div>
              </div>
              <div className="card-body table-responsive p-0">
                {stats?.recentUsers?.length > 0 ? (
                  <table className="table table-striped table-valign-middle">
                    <thead>
                      <tr>
                        <th>User</th>
                        <th>Role</th>
                        <th>Status</th>
                        <th>Created</th>
                        <th>Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {stats.recentUsers.map((user) => (
                        <tr key={user.id}>
                          <td>
                            <div className="d-flex align-items-center">
                              <div className="user-image-placeholder me-3">
                                {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                              </div>
                              <div>
                                <div className="fw-bold">{user.firstName} {user.lastName}</div>
                                <small className="text-muted">{user.email}</small>
                              </div>
                            </div>
                          </td>
                          <td>
                            <span className={`badge ${user.role === 'admin' ? 'text-bg-primary' : 'text-bg-secondary'}`}>
                              {user.role}
                            </span>
                          </td>
                          <td>
                            <span className={`badge ${user.isActive ? 'text-bg-success' : 'text-bg-danger'}`}>
                              {user.isActive ? 'Active' : 'Inactive'}
                            </span>
                          </td>
                          <td>
                            <small className="text-muted">
                              {new Date(user.createdAt).toLocaleDateString()}
                            </small>
                          </td>
                          <td>
                            <div className="btn-group btn-group-sm">
                              <button className="btn btn-info">
                                <i className="bi bi-eye"></i>
                              </button>
                              <button className="btn btn-warning">
                                <i className="bi bi-pencil"></i>
                              </button>
                              <button className="btn btn-danger">
                                <i className="bi bi-trash"></i>
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                ) : (
                  <div className="text-center py-4">
                    <i className="bi bi-people text-muted" style={{fontSize: '3rem'}}></i>
                    <h5 className="mt-2 text-muted">No recent users</h5>
                    <p className="text-muted">Users will appear here once they register</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </AdminLTELayout>
  );
};

export default AdminLTEDashboard;
