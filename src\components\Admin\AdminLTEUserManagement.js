import React, { useState, useEffect } from 'react';
import AdminController from '../../controllers/AdminController';
import AdminLTELayout from '../Layout/AdminLTELayout';

const AdminLTEUserManagement = () => {
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [editingUser, setEditingUser] = useState(null);
  const [formData, setFormData] = useState({
    username: '',
    password: '',
    email: '',
    firstName: '',
    lastName: '',
    role: 'user',
    isActive: true
  });
  const [formErrors, setFormErrors] = useState({});
  const [submitLoading, setSubmitLoading] = useState(false);

  const adminController = new AdminController();

  useEffect(() => {
    loadUsers();
  }, []);

  const loadUsers = async () => {
    try {
      setLoading(true);
      const result = adminController.getAllUsers();
      
      if (result.success) {
        setUsers(result.data);
        setError(null);
      } else {
        setError(result.message);
      }
    } catch (err) {
      console.error('Error loading users:', err);
      setError('Failed to load users');
    } finally {
      setLoading(false);
    }
  };

  const handleCreateUser = () => {
    setEditingUser(null);
    setFormData({
      username: '',
      password: '',
      email: '',
      firstName: '',
      lastName: '',
      role: 'user',
      isActive: true
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleEditUser = (user) => {
    setEditingUser(user);
    setFormData({
      username: user.username,
      password: '',
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      isActive: user.isActive
    });
    setFormErrors({});
    setShowModal(true);
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user?')) {
      try {
        const result = adminController.deleteUser(userId);
        
        if (result.success) {
          await loadUsers();
          // Show success toast (you can implement toast notifications)
          alert('User deleted successfully');
        } else {
          alert(result.message);
        }
      } catch (err) {
        console.error('Error deleting user:', err);
        alert('Failed to delete user');
      }
    }
  };

  const handleFormChange = (e) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value
    }));
    
    if (formErrors[name]) {
      setFormErrors(prev => ({
        ...prev,
        [name]: null
      }));
    }
  };

  const handleFormSubmit = async (e) => {
    e.preventDefault();
    setSubmitLoading(true);
    setFormErrors({});

    try {
      let result;
      
      if (editingUser) {
        const updateData = { ...formData };
        if (!updateData.password) {
          delete updateData.password;
        }
        result = adminController.updateUser(editingUser.id, updateData);
      } else {
        result = adminController.createUser(formData);
      }

      if (result.success) {
        setShowModal(false);
        await loadUsers();
        alert(result.message);
      } else {
        if (result.errors) {
          setFormErrors(result.errors);
        } else {
          alert(result.message);
        }
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      alert('An error occurred while saving user');
    } finally {
      setSubmitLoading(false);
    }
  };

  const closeModal = () => {
    setShowModal(false);
    setEditingUser(null);
    setFormData({
      username: '',
      password: '',
      email: '',
      firstName: '',
      lastName: '',
      role: 'user',
      isActive: true
    });
    setFormErrors({});
  };

  if (loading) {
    return (
      <AdminLTELayout>
        <div className="d-flex justify-content-center align-items-center" style={{height: '400px'}}>
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
        </div>
      </AdminLTELayout>
    );
  }

  return (
    <AdminLTELayout>
      <div className="fade-in">
        {error && (
          <div className="alert alert-danger alert-dismissible">
            <button type="button" className="btn-close" data-bs-dismiss="alert"></button>
            <h5><i className="icon bi bi-ban"></i> Error!</h5>
            {error}
          </div>
        )}

        {/* Users Card */}
        <div className="card">
          <div className="card-header">
            <h3 className="card-title">
              <i className="bi bi-people-fill me-1"></i>
              User Management ({users.length})
            </h3>
            <div className="card-tools">
              <button 
                onClick={handleCreateUser}
                className="btn btn-primary btn-sm"
              >
                <i className="bi bi-plus-circle me-1"></i>
                Add User
              </button>
            </div>
          </div>
          
          <div className="card-body table-responsive p-0">
            {users.length > 0 ? (
              <table className="table table-striped table-valign-middle">
                <thead>
                  <tr>
                    <th>User</th>
                    <th>Role</th>
                    <th>Status</th>
                    <th>Created</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {users.map((user) => (
                    <tr key={user.id}>
                      <td>
                        <div className="d-flex align-items-center">
                          <div className="user-image-placeholder me-3">
                            {user.firstName?.charAt(0)}{user.lastName?.charAt(0)}
                          </div>
                          <div>
                            <div className="fw-bold">{user.firstName} {user.lastName}</div>
                            <small className="text-muted">{user.email}</small>
                            <br />
                            <small className="text-muted">@{user.username}</small>
                          </div>
                        </div>
                      </td>
                      <td>
                        <span className={`badge ${user.role === 'admin' ? 'text-bg-primary' : 'text-bg-secondary'}`}>
                          {user.role}
                        </span>
                      </td>
                      <td>
                        <span className={`badge ${user.isActive ? 'text-bg-success' : 'text-bg-danger'}`}>
                          {user.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                      <td>
                        <small className="text-muted">
                          {new Date(user.createdAt).toLocaleDateString()}
                        </small>
                      </td>
                      <td>
                        <small className="text-muted">
                          {user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never'}
                        </small>
                      </td>
                      <td>
                        <div className="btn-group btn-group-sm">
                          <button 
                            onClick={() => handleEditUser(user)}
                            className="btn btn-info"
                            title="Edit"
                          >
                            <i className="bi bi-pencil"></i>
                          </button>
                          <button 
                            onClick={() => handleDeleteUser(user.id)}
                            className="btn btn-danger"
                            title="Delete"
                          >
                            <i className="bi bi-trash"></i>
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            ) : (
              <div className="text-center py-5">
                <i className="bi bi-people text-muted" style={{fontSize: '4rem'}}></i>
                <h4 className="mt-3 text-muted">No users found</h4>
                <p className="text-muted">Get started by creating your first user.</p>
                <button 
                  onClick={handleCreateUser}
                  className="btn btn-primary"
                >
                  <i className="bi bi-plus-circle me-1"></i>
                  Create First User
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Modal */}
        {showModal && (
          <div className="modal fade show d-block" tabIndex="-1" style={{backgroundColor: 'rgba(0,0,0,0.5)'}}>
            <div className="modal-dialog">
              <div className="modal-content">
                <div className="modal-header">
                  <h4 className="modal-title">
                    {editingUser ? 'Edit User' : 'Create New User'}
                  </h4>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={closeModal}
                  ></button>
                </div>

                <form onSubmit={handleFormSubmit}>
                  <div className="modal-body">
                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">Username</label>
                          <input
                            type="text"
                            name="username"
                            value={formData.username}
                            onChange={handleFormChange}
                            className={`form-control ${formErrors.username ? 'is-invalid' : ''}`}
                            required
                          />
                          {formErrors.username && (
                            <div className="invalid-feedback">{formErrors.username[0]}</div>
                          )}
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">
                            Password {editingUser && '(leave blank to keep current)'}
                          </label>
                          <input
                            type="password"
                            name="password"
                            value={formData.password}
                            onChange={handleFormChange}
                            className={`form-control ${formErrors.password ? 'is-invalid' : ''}`}
                            required={!editingUser}
                          />
                          {formErrors.password && (
                            <div className="invalid-feedback">{formErrors.password[0]}</div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="mb-3">
                      <label className="form-label">Email</label>
                      <input
                        type="email"
                        name="email"
                        value={formData.email}
                        onChange={handleFormChange}
                        className={`form-control ${formErrors.email ? 'is-invalid' : ''}`}
                        required
                      />
                      {formErrors.email && (
                        <div className="invalid-feedback">{formErrors.email[0]}</div>
                      )}
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">First Name</label>
                          <input
                            type="text"
                            name="firstName"
                            value={formData.firstName}
                            onChange={handleFormChange}
                            className={`form-control ${formErrors.firstName ? 'is-invalid' : ''}`}
                            required
                          />
                          {formErrors.firstName && (
                            <div className="invalid-feedback">{formErrors.firstName[0]}</div>
                          )}
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">Last Name</label>
                          <input
                            type="text"
                            name="lastName"
                            value={formData.lastName}
                            onChange={handleFormChange}
                            className={`form-control ${formErrors.lastName ? 'is-invalid' : ''}`}
                            required
                          />
                          {formErrors.lastName && (
                            <div className="invalid-feedback">{formErrors.lastName[0]}</div>
                          )}
                        </div>
                      </div>
                    </div>

                    <div className="row">
                      <div className="col-md-6">
                        <div className="mb-3">
                          <label className="form-label">Role</label>
                          <select
                            name="role"
                            value={formData.role}
                            onChange={handleFormChange}
                            className="form-select"
                          >
                            <option value="user">User</option>
                            <option value="admin">Admin</option>
                          </select>
                        </div>
                      </div>

                      <div className="col-md-6">
                        <div className="mb-3">
                          <div className="form-check form-switch mt-4">
                            <input
                              type="checkbox"
                              name="isActive"
                              checked={formData.isActive}
                              onChange={handleFormChange}
                              className="form-check-input"
                              id="isActiveSwitch"
                            />
                            <label className="form-check-label" htmlFor="isActiveSwitch">
                              Active
                            </label>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="modal-footer">
                    <button
                      type="button"
                      className="btn btn-secondary"
                      onClick={closeModal}
                    >
                      Cancel
                    </button>
                    <button
                      type="submit"
                      className="btn btn-primary"
                      disabled={submitLoading}
                    >
                      {submitLoading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Saving...
                        </>
                      ) : (
                        editingUser ? 'Update User' : 'Create User'
                      )}
                    </button>
                  </div>
                </form>
              </div>
            </div>
          </div>
        )}
      </div>
    </AdminLTELayout>
  );
};

export default AdminLTEUserManagement;
