import React, { useState, useEffect } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const AdminLTELayout = ({ children }) => {
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const { user, logout } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    // Initialize AdminLTE after component mounts
    if (window.AdminLTE) {
      window.AdminLTE.init();
    }
  }, []);

  const handleLogout = async () => {
    try {
      await logout();
      navigate('/login');
    } catch (error) {
      console.error('Logout error:', error);
    }
  };

  const navigation = [
    {
      name: 'Dashboard',
      href: '/admin/dashboard',
      icon: 'bi bi-speedometer',
      active: location.pathname === '/admin/dashboard'
    },
    {
      name: 'Users',
      href: '/admin/users',
      icon: 'bi bi-people-fill',
      active: location.pathname === '/admin/users'
    },
    {
      name: 'Analytics',
      href: '/admin/analytics',
      icon: 'bi bi-bar-chart-fill',
      active: location.pathname === '/admin/analytics'
    },
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: 'bi bi-gear-fill',
      active: location.pathname === '/admin/settings'
    }
  ];

  return (
    <div className="app-wrapper">
      {/* Header */}
      <nav className="app-header navbar navbar-expand bg-body">
        <div className="container-fluid">
          {/* Start Navbar Links */}
          <ul className="navbar-nav">
            <li className="nav-item">
              <a 
                className="nav-link" 
                data-lte-toggle="sidebar" 
                href="#" 
                role="button"
                onClick={(e) => {
                  e.preventDefault();
                  setSidebarCollapsed(!sidebarCollapsed);
                }}
              >
                <i className="bi bi-list"></i>
              </a>
            </li>
            <li className="nav-item d-none d-md-block">
              <Link to="/admin/dashboard" className="nav-link">Home</Link>
            </li>
            <li className="nav-item d-none d-md-block">
              <Link to="/admin/users" className="nav-link">Users</Link>
            </li>
          </ul>

          {/* End Navbar Links */}
          <ul className="navbar-nav ms-auto">
            {/* Notifications */}
            <li className="nav-item dropdown">
              <a className="nav-link" data-bs-toggle="dropdown" href="#">
                <i className="bi bi-bell-fill"></i>
                <span className="navbar-badge badge text-bg-warning">3</span>
              </a>
              <div className="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                <span className="dropdown-item dropdown-header">3 Notifications</span>
                <div className="dropdown-divider"></div>
                <a href="#" className="dropdown-item">
                  <i className="bi bi-envelope me-2"></i> New user registered
                  <span className="float-end text-secondary fs-7">2 mins</span>
                </a>
                <div className="dropdown-divider"></div>
                <a href="#" className="dropdown-item">
                  <i className="bi bi-people-fill me-2"></i> System update available
                  <span className="float-end text-secondary fs-7">1 hour</span>
                </a>
                <div className="dropdown-divider"></div>
                <a href="#" className="dropdown-item dropdown-footer">See All Notifications</a>
              </div>
            </li>

            {/* Fullscreen Toggle */}
            <li className="nav-item">
              <a className="nav-link" href="#" data-lte-toggle="fullscreen">
                <i data-lte-icon="maximize" className="bi bi-arrows-fullscreen"></i>
                <i data-lte-icon="minimize" className="bi bi-fullscreen-exit" style={{display: 'none'}}></i>
              </a>
            </li>

            {/* User Menu */}
            <li className="nav-item dropdown user-menu">
              <a href="#" className="nav-link dropdown-toggle" data-bs-toggle="dropdown">
                <div className="user-image-wrapper">
                  <div className="user-image-placeholder">
                    {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                  </div>
                </div>
                <span className="d-none d-md-inline">{user?.firstName} {user?.lastName}</span>
              </a>
              <ul className="dropdown-menu dropdown-menu-lg dropdown-menu-end">
                <li className="user-header text-bg-primary">
                  <div className="user-image-placeholder large">
                    {user?.firstName?.charAt(0)}{user?.lastName?.charAt(0)}
                  </div>
                  <p>
                    {user?.firstName} {user?.lastName} - {user?.role}
                    <small>Member since {new Date().getFullYear()}</small>
                  </p>
                </li>
                <li className="user-body">
                  <div className="row">
                    <div className="col-4 text-center"><a href="#">Profile</a></div>
                    <div className="col-4 text-center"><a href="#">Settings</a></div>
                    <div className="col-4 text-center"><a href="#">Help</a></div>
                  </div>
                </li>
                <li className="user-footer">
                  <a href="#" className="btn btn-default btn-flat">Profile</a>
                  <button 
                    onClick={handleLogout}
                    className="btn btn-default btn-flat float-end"
                  >
                    Sign out
                  </button>
                </li>
              </ul>
            </li>
          </ul>
        </div>
      </nav>

      {/* Sidebar */}
      <aside className={`app-sidebar bg-body-secondary shadow ${sidebarCollapsed ? 'sidebar-collapse' : ''}`} data-bs-theme="dark">
        {/* Sidebar Brand */}
        <div className="sidebar-brand">
          <Link to="/admin/dashboard" className="brand-link">
            <div className="brand-image-placeholder">
              <i className="bi bi-speedometer"></i>
            </div>
            <span className="brand-text fw-light">AdminLTE v4</span>
          </Link>
        </div>

        {/* Sidebar Wrapper */}
        <div className="sidebar-wrapper">
          <nav className="mt-2">
            <ul className="nav sidebar-menu flex-column" data-lte-toggle="treeview" role="menu" data-accordion="false">
              {navigation.map((item) => (
                <li key={item.name} className={`nav-item ${item.active ? 'menu-open' : ''}`}>
                  <Link to={item.href} className={`nav-link ${item.active ? 'active' : ''}`}>
                    <i className={`nav-icon ${item.icon}`}></i>
                    <p>{item.name}</p>
                  </Link>
                </li>
              ))}
              
              <li className="nav-header">SYSTEM</li>
              <li className="nav-item">
                <a href="#" className="nav-link">
                  <i className="nav-icon bi bi-gear-fill"></i>
                  <p>Settings</p>
                </a>
              </li>
              <li className="nav-item">
                <button onClick={handleLogout} className="nav-link btn btn-link text-start w-100">
                  <i className="nav-icon bi bi-box-arrow-right"></i>
                  <p>Logout</p>
                </button>
              </li>
            </ul>
          </nav>
        </div>
      </aside>

      {/* Content Wrapper */}
      <main className="app-main">
        <div className="app-content-header">
          <div className="container-fluid">
            <div className="row">
              <div className="col-sm-6">
                <h3 className="mb-0">
                  {navigation.find(item => item.active)?.name || 'Dashboard'}
                </h3>
              </div>
              <div className="col-sm-6">
                <ol className="breadcrumb float-sm-end">
                  <li className="breadcrumb-item"><Link to="/admin/dashboard">Home</Link></li>
                  <li className="breadcrumb-item active" aria-current="page">
                    {navigation.find(item => item.active)?.name || 'Dashboard'}
                  </li>
                </ol>
              </div>
            </div>
          </div>
        </div>

        <div className="app-content">
          <div className="container-fluid">
            {children}
          </div>
        </div>
      </main>
    </div>
  );
};

export default AdminLTELayout;
