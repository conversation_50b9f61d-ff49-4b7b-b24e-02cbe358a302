import React, { useState, useEffect } from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';

const AdminLTELoginForm = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: ''
  });
  const [showPassword, setShowPassword] = useState(false);
  
  const { login, isAuthenticated, loading, error, clearError } = useAuth();
  const location = useLocation();
  
  const from = location.state?.from?.pathname || '/';

  useEffect(() => {
    if (error) {
      clearError();
    }
  }, [formData, clearError]);

  if (isAuthenticated) {
    return <Navigate to={from} replace />;
  }

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (!formData.username || !formData.password) {
      return;
    }

    await login(formData.username, formData.password);
  };

  return (
    <div className="login-page" style={{
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      minHeight: '100vh'
    }}>
      <div className="login-box">
        <div className="login-logo">
          <div className="d-flex align-items-center justify-content-center mb-3">
            <div className="brand-image-placeholder me-2">
              <i className="bi bi-speedometer"></i>
            </div>
            <b>Admin</b>LTE v4
          </div>
        </div>

        <div className="card">
          <div className="card-body login-card-body">
            <p className="login-box-msg">Sign in to start your session</p>

            {error && (
              <div className="alert alert-danger alert-dismissible">
                <button type="button" className="btn-close" onClick={clearError}></button>
                <h5><i className="icon bi bi-ban"></i> Alert!</h5>
                {error}
              </div>
            )}

            <form onSubmit={handleSubmit}>
              <div className="input-group mb-3">
                <input
                  type="text"
                  name="username"
                  className="form-control"
                  placeholder="Username"
                  value={formData.username}
                  onChange={handleChange}
                  disabled={loading}
                  required
                />
                <div className="input-group-text">
                  <span className="bi bi-person-fill"></span>
                </div>
              </div>

              <div className="input-group mb-3">
                <input
                  type={showPassword ? 'text' : 'password'}
                  name="password"
                  className="form-control"
                  placeholder="Password"
                  value={formData.password}
                  onChange={handleChange}
                  disabled={loading}
                  required
                />
                <div className="input-group-text">
                  <button
                    type="button"
                    className="btn btn-link p-0 border-0"
                    onClick={() => setShowPassword(!showPassword)}
                    style={{color: '#6c757d'}}
                  >
                    <span className={`bi ${showPassword ? 'bi-eye-slash-fill' : 'bi-eye-fill'}`}></span>
                  </button>
                </div>
              </div>

              <div className="row">
                <div className="col-8">
                  <div className="form-check">
                    <input className="form-check-input" type="checkbox" id="remember" />
                    <label className="form-check-label" htmlFor="remember">
                      Remember Me
                    </label>
                  </div>
                </div>
                <div className="col-4">
                  <div className="d-grid gap-2">
                    <button 
                      type="submit" 
                      className="btn btn-primary"
                      disabled={loading || !formData.username || !formData.password}
                    >
                      {loading ? (
                        <>
                          <span className="spinner-border spinner-border-sm me-2"></span>
                          Signing in...
                        </>
                      ) : (
                        'Sign In'
                      )}
                    </button>
                  </div>
                </div>
              </div>
            </form>

            <div className="social-auth-links text-center mt-2 mb-3">
              <a href="#" className="btn btn-block btn-primary">
                <i className="bi bi-facebook me-2"></i> Sign in using Facebook
              </a>
              <a href="#" className="btn btn-block btn-danger">
                <i className="bi bi-google me-2"></i> Sign in using Google+
              </a>
            </div>

            <p className="mb-1">
              <a href="#">I forgot my password</a>
            </p>
            <p className="mb-0">
              <a href="#" className="text-center">Register a new membership</a>
            </p>
          </div>
        </div>

        {/* Demo Credentials */}
        <div className="card mt-3">
          <div className="card-header">
            <h3 className="card-title">
              <i className="bi bi-info-circle me-1"></i>
              Demo Credentials
            </h3>
          </div>
          <div className="card-body">
            <div className="row">
              <div className="col-md-6">
                <div className="info-box">
                  <span className="info-box-icon text-bg-primary">
                    <i className="bi bi-shield-fill-check"></i>
                  </span>
                  <div className="info-box-content">
                    <span className="info-box-text">Admin</span>
                    <span className="info-box-number">admin / admin123</span>
                  </div>
                </div>
              </div>
              <div className="col-md-6">
                <div className="info-box">
                  <span className="info-box-icon text-bg-success">
                    <i className="bi bi-person-fill"></i>
                  </span>
                  <div className="info-box-content">
                    <span className="info-box-text">User</span>
                    <span className="info-box-number">user1 / user123</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AdminLTELoginForm;
