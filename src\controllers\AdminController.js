import UserModel from '../models/UserModel';
import SessionModel from '../models/SessionModel';
import AuthService from '../services/AuthService';
import { validateUserForm } from '../utils/validation';
import { USER_ROLES } from '../utils/constants';

/**
 * Admin Controller
 */
class AdminController {
  constructor() {
    this.userModel = new UserModel();
    this.sessionModel = new SessionModel();
    this.authService = new AuthService();
  }

  /**
   * Check if current user has admin access
   * @returns {boolean} true if user is admin
   */
  hasAdminAccess() {
    return this.authService.isAdmin();
  }

  /**
   * Get all users
   * @returns {object} response with users data
   */
  getAllUsers() {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      const users = this.userModel.getAllUsers().map(({ password, ...user }) => user);
      
      return {
        success: true,
        data: users
      };
    } catch (error) {
      console.error('AdminController getAllUsers error:', error);
      return {
        success: false,
        message: 'An error occurred while fetching users'
      };
    }
  }

  /**
   * Get user by ID
   * @param {number} userId 
   * @returns {object} response with user data
   */
  getUserById(userId) {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      const user = this.userModel.getUserById(userId);
      
      if (!user) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Remove password from response
      const { password, ...userWithoutPassword } = user;
      
      return {
        success: true,
        data: userWithoutPassword
      };
    } catch (error) {
      console.error('AdminController getUserById error:', error);
      return {
        success: false,
        message: 'An error occurred while fetching user'
      };
    }
  }

  /**
   * Create new user
   * @param {object} userData 
   * @returns {object} response with created user data
   */
  createUser(userData) {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      // Validate user data
      const validation = validateUserForm(userData);
      
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Validation failed',
          errors: validation.errors
        };
      }

      // Create user
      const newUser = this.userModel.createUser(userData);
      
      return {
        success: true,
        message: 'User created successfully',
        data: newUser
      };
    } catch (error) {
      console.error('AdminController createUser error:', error);
      return {
        success: false,
        message: error.message || 'An error occurred while creating user'
      };
    }
  }

  /**
   * Update user
   * @param {number} userId 
   * @param {object} updateData 
   * @returns {object} response with updated user data
   */
  updateUser(userId, updateData) {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      // Don't validate password if it's not being updated
      const validationData = { ...updateData };
      if (!updateData.password) {
        delete validationData.password;
      }

      // Validate user data
      const validation = validateUserForm(validationData);
      
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Validation failed',
          errors: validation.errors
        };
      }

      // Update user
      const updatedUser = this.userModel.updateUser(userId, updateData);
      
      if (!updatedUser) {
        return {
          success: false,
          message: 'User not found'
        };
      }
      
      return {
        success: true,
        message: 'User updated successfully',
        data: updatedUser
      };
    } catch (error) {
      console.error('AdminController updateUser error:', error);
      return {
        success: false,
        message: error.message || 'An error occurred while updating user'
      };
    }
  }

  /**
   * Delete user
   * @param {number} userId 
   * @returns {object} response
   */
  deleteUser(userId) {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      // Check if trying to delete current user
      const currentUser = this.authService.getCurrentUser();
      if (currentUser && currentUser.id === parseInt(userId)) {
        return {
          success: false,
          message: 'Cannot delete your own account'
        };
      }

      // Delete user
      const deleted = this.userModel.deleteUser(userId);
      
      if (!deleted) {
        return {
          success: false,
          message: 'User not found'
        };
      }

      // Deactivate all sessions for the deleted user
      this.sessionModel.deactivateUserSessions(parseInt(userId));
      
      return {
        success: true,
        message: 'User deleted successfully'
      };
    } catch (error) {
      console.error('AdminController deleteUser error:', error);
      return {
        success: false,
        message: 'An error occurred while deleting user'
      };
    }
  }

  /**
   * Get dashboard statistics
   * @returns {object} response with dashboard data
   */
  getDashboardStats() {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      const allUsers = this.userModel.getAllUsers();
      const activeSessions = this.sessionModel.getActiveSessions();
      
      const stats = {
        totalUsers: allUsers.length,
        activeUsers: allUsers.filter(user => user.isActive).length,
        inactiveUsers: allUsers.filter(user => !user.isActive).length,
        adminUsers: allUsers.filter(user => user.role === USER_ROLES.ADMIN).length,
        regularUsers: allUsers.filter(user => user.role === USER_ROLES.USER).length,
        activeSessions: activeSessions.length,
        recentUsers: allUsers
          .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
          .slice(0, 5)
          .map(({ password, ...user }) => user)
      };
      
      return {
        success: true,
        data: stats
      };
    } catch (error) {
      console.error('AdminController getDashboardStats error:', error);
      return {
        success: false,
        message: 'An error occurred while fetching dashboard statistics'
      };
    }
  }

  /**
   * Get active sessions
   * @returns {object} response with sessions data
   */
  getActiveSessions() {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      const sessions = this.sessionModel.getActiveSessions();
      
      return {
        success: true,
        data: sessions
      };
    } catch (error) {
      console.error('AdminController getActiveSessions error:', error);
      return {
        success: false,
        message: 'An error occurred while fetching active sessions'
      };
    }
  }

  /**
   * Deactivate session
   * @param {string} sessionId 
   * @returns {object} response
   */
  deactivateSession(sessionId) {
    try {
      if (!this.hasAdminAccess()) {
        return {
          success: false,
          message: 'Access denied. Admin privileges required.'
        };
      }

      const deactivated = this.sessionModel.deactivateSession(sessionId);
      
      if (!deactivated) {
        return {
          success: false,
          message: 'Session not found'
        };
      }
      
      return {
        success: true,
        message: 'Session deactivated successfully'
      };
    } catch (error) {
      console.error('AdminController deactivateSession error:', error);
      return {
        success: false,
        message: 'An error occurred while deactivating session'
      };
    }
  }
}

export default AdminController;
