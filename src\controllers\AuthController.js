import AuthService from '../services/AuthService';
import { validateLoginForm } from '../utils/validation';

/**
 * Authentication Controller
 */
class AuthController {
  constructor() {
    this.authService = new AuthService();
  }

  /**
   * Handle login request
   * @param {object} loginData 
   * @returns {Promise<object>} login response
   */
  async handleLogin(loginData) {
    try {
      // Validate input
      const validation = validateLoginForm(loginData);
      
      if (!validation.isValid) {
        return {
          success: false,
          message: 'Validation failed',
          errors: validation.errors
        };
      }

      // Attempt login
      const result = await this.authService.login(loginData.username, loginData.password);
      
      return result;
    } catch (error) {
      console.error('AuthController login error:', error);
      return {
        success: false,
        message: 'An unexpected error occurred during login'
      };
    }
  }

  /**
   * Handle logout request
   * @returns {Promise<object>} logout response
   */
  async handleLogout() {
    try {
      const result = await this.authService.logout();
      return result;
    } catch (error) {
      console.error('AuthController logout error:', error);
      return {
        success: false,
        message: 'An unexpected error occurred during logout'
      };
    }
  }

  /**
   * Get current user
   * @returns {object|null} current user or null
   */
  getCurrentUser() {
    try {
      return this.authService.getCurrentUser();
    } catch (error) {
      console.error('AuthController getCurrentUser error:', error);
      return null;
    }
  }

  /**
   * Check authentication status
   * @returns {object} authentication status
   */
  checkAuthStatus() {
    try {
      const isAuthenticated = this.authService.isAuthenticated();
      const user = isAuthenticated ? this.authService.getCurrentUser() : null;
      
      return {
        isAuthenticated,
        user,
        isAdmin: this.authService.isAdmin()
      };
    } catch (error) {
      console.error('AuthController checkAuthStatus error:', error);
      return {
        isAuthenticated: false,
        user: null,
        isAdmin: false
      };
    }
  }

  /**
   * Handle password change request
   * @param {object} passwordData 
   * @returns {Promise<object>} password change response
   */
  async handlePasswordChange(passwordData) {
    try {
      const { currentPassword, newPassword, confirmPassword } = passwordData;

      // Basic validation
      if (!currentPassword || !newPassword || !confirmPassword) {
        return {
          success: false,
          message: 'All password fields are required'
        };
      }

      if (newPassword !== confirmPassword) {
        return {
          success: false,
          message: 'New password and confirmation do not match'
        };
      }

      if (newPassword.length < 6) {
        return {
          success: false,
          message: 'New password must be at least 6 characters long'
        };
      }

      // Change password
      const result = await this.authService.changePassword(currentPassword, newPassword);
      
      return result;
    } catch (error) {
      console.error('AuthController handlePasswordChange error:', error);
      return {
        success: false,
        message: 'An unexpected error occurred while changing password'
      };
    }
  }

  /**
   * Handle profile update request
   * @param {object} profileData 
   * @returns {Promise<object>} profile update response
   */
  async handleProfileUpdate(profileData) {
    try {
      // Basic validation
      if (!profileData.firstName || !profileData.lastName || !profileData.email) {
        return {
          success: false,
          message: 'First name, last name, and email are required'
        };
      }

      // Email format validation
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(profileData.email)) {
        return {
          success: false,
          message: 'Please enter a valid email address'
        };
      }

      // Update profile
      const result = await this.authService.updateProfile(profileData);
      
      return result;
    } catch (error) {
      console.error('AuthController handleProfileUpdate error:', error);
      return {
        success: false,
        message: 'An unexpected error occurred while updating profile'
      };
    }
  }

  /**
   * Refresh session
   * @returns {boolean} true if session refreshed successfully
   */
  refreshSession() {
    try {
      return this.authService.refreshSession();
    } catch (error) {
      console.error('AuthController refreshSession error:', error);
      return false;
    }
  }

  /**
   * Check if user has admin privileges
   * @returns {boolean} true if user is admin
   */
  isAdmin() {
    try {
      return this.authService.isAdmin();
    } catch (error) {
      console.error('AuthController isAdmin error:', error);
      return false;
    }
  }

  /**
   * Check if user has specific role
   * @param {string} role 
   * @returns {boolean} true if user has the role
   */
  hasRole(role) {
    try {
      return this.authService.hasRole(role);
    } catch (error) {
      console.error('AuthController hasRole error:', error);
      return false;
    }
  }
}

export default AuthController;
