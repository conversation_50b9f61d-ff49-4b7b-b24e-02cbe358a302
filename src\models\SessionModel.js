import sessionsData from '../data/sessions.json';

/**
 * Session Model for handling session data operations
 */
class SessionModel {
  constructor() {
    this.sessions = this.loadSessions();
  }

  /**
   * Load sessions from localStorage or use default data
   * @returns {Array} sessions array
   */
  loadSessions() {
    try {
      const storedSessions = localStorage.getItem('sessions');
      if (storedSessions) {
        return JSON.parse(storedSessions);
      }
      // If no stored sessions, use default data and save it
      this.saveSessions(sessionsData);
      return sessionsData;
    } catch (error) {
      console.error('Error loading sessions:', error);
      return sessionsData;
    }
  }

  /**
   * Save sessions to localStorage
   * @param {Array} sessions 
   */
  saveSessions(sessions = this.sessions) {
    try {
      localStorage.setItem('sessions', JSON.stringify(sessions));
      this.sessions = sessions;
    } catch (error) {
      console.error('Error saving sessions:', error);
    }
  }

  /**
   * Generate unique session ID
   * @returns {string} session ID
   */
  generateSessionId() {
    return 'session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
  }

  /**
   * Create new session
   * @param {number} userId 
   * @param {string} username 
   * @returns {object} session object
   */
  createSession(userId, username) {
    const sessionId = this.generateSessionId();
    const session = {
      id: sessionId,
      userId: userId,
      username: username,
      createdAt: new Date().toISOString(),
      lastActivity: new Date().toISOString(),
      isActive: true,
      expiresAt: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
    };

    this.sessions.push(session);
    this.saveSessions();
    
    return session;
  }

  /**
   * Get session by ID
   * @param {string} sessionId 
   * @returns {object|null} session object or null
   */
  getSessionById(sessionId) {
    return this.sessions.find(session => session.id === sessionId) || null;
  }

  /**
   * Get active session by user ID
   * @param {number} userId 
   * @returns {object|null} session object or null
   */
  getActiveSessionByUserId(userId) {
    return this.sessions.find(session => 
      session.userId === userId && 
      session.isActive && 
      new Date(session.expiresAt) > new Date()
    ) || null;
  }

  /**
   * Update session activity
   * @param {string} sessionId 
   * @returns {boolean} true if updated, false if not found
   */
  updateSessionActivity(sessionId) {
    const session = this.getSessionById(sessionId);
    
    if (session && session.isActive) {
      session.lastActivity = new Date().toISOString();
      // Extend expiration by 24 hours from last activity
      session.expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString();
      this.saveSessions();
      return true;
    }
    
    return false;
  }

  /**
   * Validate session
   * @param {string} sessionId 
   * @returns {boolean} true if valid, false if invalid or expired
   */
  validateSession(sessionId) {
    const session = this.getSessionById(sessionId);
    
    if (!session || !session.isActive) {
      return false;
    }
    
    // Check if session is expired
    if (new Date(session.expiresAt) <= new Date()) {
      this.deactivateSession(sessionId);
      return false;
    }
    
    // Update last activity
    this.updateSessionActivity(sessionId);
    return true;
  }

  /**
   * Deactivate session
   * @param {string} sessionId 
   * @returns {boolean} true if deactivated, false if not found
   */
  deactivateSession(sessionId) {
    const session = this.getSessionById(sessionId);
    
    if (session) {
      session.isActive = false;
      session.deactivatedAt = new Date().toISOString();
      this.saveSessions();
      return true;
    }
    
    return false;
  }

  /**
   * Deactivate all sessions for a user
   * @param {number} userId 
   * @returns {number} number of sessions deactivated
   */
  deactivateUserSessions(userId) {
    let deactivatedCount = 0;
    
    this.sessions.forEach(session => {
      if (session.userId === userId && session.isActive) {
        session.isActive = false;
        session.deactivatedAt = new Date().toISOString();
        deactivatedCount++;
      }
    });
    
    if (deactivatedCount > 0) {
      this.saveSessions();
    }
    
    return deactivatedCount;
  }

  /**
   * Clean up expired sessions
   * @returns {number} number of sessions cleaned up
   */
  cleanupExpiredSessions() {
    const now = new Date();
    let cleanedCount = 0;
    
    this.sessions.forEach(session => {
      if (session.isActive && new Date(session.expiresAt) <= now) {
        session.isActive = false;
        session.deactivatedAt = now.toISOString();
        cleanedCount++;
      }
    });
    
    if (cleanedCount > 0) {
      this.saveSessions();
    }
    
    return cleanedCount;
  }

  /**
   * Get all active sessions
   * @returns {Array} active sessions array
   */
  getActiveSessions() {
    const now = new Date();
    return this.sessions.filter(session => 
      session.isActive && new Date(session.expiresAt) > now
    );
  }

  /**
   * Get sessions by user ID
   * @param {number} userId 
   * @returns {Array} sessions array
   */
  getSessionsByUserId(userId) {
    return this.sessions.filter(session => session.userId === userId);
  }
}

export default SessionModel;
