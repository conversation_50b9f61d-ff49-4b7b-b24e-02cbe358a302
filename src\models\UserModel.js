import usersData from '../data/users.json';

/**
 * User Model for handling user data operations
 */
class UserModel {
  constructor() {
    // In a real application, this would be stored in a database
    // For this demo, we'll use localStorage to persist changes
    this.users = this.loadUsers();
  }

  /**
   * Load users from localStorage or use default data
   * @returns {Array} users array
   */
  loadUsers() {
    try {
      const storedUsers = localStorage.getItem('users');
      if (storedUsers) {
        return JSON.parse(storedUsers);
      }
      // If no stored users, use default data and save it
      this.saveUsers(usersData);
      return usersData;
    } catch (error) {
      console.error('Error loading users:', error);
      return usersData;
    }
  }

  /**
   * Save users to localStorage
   * @param {Array} users 
   */
  saveUsers(users = this.users) {
    try {
      localStorage.setItem('users', JSON.stringify(users));
      this.users = users;
    } catch (error) {
      console.error('Error saving users:', error);
    }
  }

  /**
   * Get all users
   * @returns {Array} users array
   */
  getAllUsers() {
    return this.users;
  }

  /**
   * Get user by ID
   * @param {number} id 
   * @returns {object|null} user object or null
   */
  getUserById(id) {
    return this.users.find(user => user.id === parseInt(id)) || null;
  }

  /**
   * Get user by username
   * @param {string} username 
   * @returns {object|null} user object or null
   */
  getUserByUsername(username) {
    return this.users.find(user => user.username === username) || null;
  }

  /**
   * Get user by email
   * @param {string} email 
   * @returns {object|null} user object or null
   */
  getUserByEmail(email) {
    return this.users.find(user => user.email === email) || null;
  }

  /**
   * Authenticate user
   * @param {string} username 
   * @param {string} password 
   * @returns {object|null} user object without password or null
   */
  authenticate(username, password) {
    const user = this.getUserByUsername(username);
    
    if (user && user.password === password && user.isActive) {
      // Update last login
      user.lastLogin = new Date().toISOString();
      this.saveUsers();
      
      // Return user without password
      const { password: _, ...userWithoutPassword } = user;
      return userWithoutPassword;
    }
    
    return null;
  }

  /**
   * Create new user
   * @param {object} userData 
   * @returns {object} created user without password
   */
  createUser(userData) {
    // Check if username or email already exists
    if (this.getUserByUsername(userData.username)) {
      throw new Error('Username already exists');
    }
    
    if (this.getUserByEmail(userData.email)) {
      throw new Error('Email already exists');
    }

    // Generate new ID
    const newId = Math.max(...this.users.map(u => u.id), 0) + 1;
    
    const newUser = {
      id: newId,
      username: userData.username,
      password: userData.password,
      email: userData.email,
      role: userData.role || 'user',
      firstName: userData.firstName,
      lastName: userData.lastName,
      isActive: userData.isActive !== undefined ? userData.isActive : true,
      createdAt: new Date().toISOString(),
      lastLogin: null
    };

    this.users.push(newUser);
    this.saveUsers();

    // Return user without password
    const { password: _, ...userWithoutPassword } = newUser;
    return userWithoutPassword;
  }

  /**
   * Update user
   * @param {number} id 
   * @param {object} updateData 
   * @returns {object|null} updated user without password or null
   */
  updateUser(id, updateData) {
    const userIndex = this.users.findIndex(user => user.id === parseInt(id));
    
    if (userIndex === -1) {
      return null;
    }

    // Check if username or email already exists (excluding current user)
    if (updateData.username) {
      const existingUser = this.getUserByUsername(updateData.username);
      if (existingUser && existingUser.id !== parseInt(id)) {
        throw new Error('Username already exists');
      }
    }
    
    if (updateData.email) {
      const existingUser = this.getUserByEmail(updateData.email);
      if (existingUser && existingUser.id !== parseInt(id)) {
        throw new Error('Email already exists');
      }
    }

    // Update user data
    this.users[userIndex] = {
      ...this.users[userIndex],
      ...updateData,
      id: parseInt(id) // Ensure ID doesn't change
    };

    this.saveUsers();

    // Return user without password
    const { password: _, ...userWithoutPassword } = this.users[userIndex];
    return userWithoutPassword;
  }

  /**
   * Delete user
   * @param {number} id 
   * @returns {boolean} true if deleted, false if not found
   */
  deleteUser(id) {
    const userIndex = this.users.findIndex(user => user.id === parseInt(id));
    
    if (userIndex === -1) {
      return false;
    }

    this.users.splice(userIndex, 1);
    this.saveUsers();
    return true;
  }

  /**
   * Get users by role
   * @param {string} role 
   * @returns {Array} users array without passwords
   */
  getUsersByRole(role) {
    return this.users
      .filter(user => user.role === role)
      .map(({ password: _, ...userWithoutPassword }) => userWithoutPassword);
  }

  /**
   * Get active users
   * @returns {Array} active users array without passwords
   */
  getActiveUsers() {
    return this.users
      .filter(user => user.isActive)
      .map(({ password: _, ...userWithoutPassword }) => userWithoutPassword);
  }
}

export default UserModel;
