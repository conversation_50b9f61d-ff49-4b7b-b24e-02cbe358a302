import UserModel from '../models/UserModel';
import SessionModel from '../models/SessionModel';
import StorageService from './StorageService';
import { USER_ROLES } from '../utils/constants';

/**
 * Authentication Service
 */
class AuthService {
  constructor() {
    this.userModel = new UserModel();
    this.sessionModel = new SessionModel();
  }

  /**
   * Login user
   * @param {string} username 
   * @param {string} password 
   * @returns {Promise<object>} login result
   */
  async login(username, password) {
    try {
      // Authenticate user
      const user = this.userModel.authenticate(username, password);
      
      if (!user) {
        return {
          success: false,
          message: 'Invalid username or password'
        };
      }

      // Deactivate any existing sessions for this user
      this.sessionModel.deactivateUserSessions(user.id);

      // Create new session
      const session = this.sessionModel.createSession(user.id, user.username);

      // Store user and session data
      StorageService.setCurrentUser(user);
      StorageService.setSessionId(session.id);
      StorageService.setAuthToken(session.id); // Using session ID as auth token

      return {
        success: true,
        message: 'Login successful',
        user: user,
        sessionId: session.id
      };
    } catch (error) {
      console.error('Login error:', error);
      return {
        success: false,
        message: 'An error occurred during login'
      };
    }
  }

  /**
   * Logout user
   * @returns {Promise<object>} logout result
   */
  async logout() {
    try {
      const sessionId = StorageService.getSessionId();
      
      if (sessionId) {
        // Deactivate session
        this.sessionModel.deactivateSession(sessionId);
      }

      // Clear storage
      StorageService.clearAuthData();

      return {
        success: true,
        message: 'Logged out successfully'
      };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        message: 'An error occurred during logout'
      };
    }
  }

  /**
   * Check if user is authenticated
   * @returns {boolean} true if authenticated
   */
  isAuthenticated() {
    const user = StorageService.getCurrentUser();
    const sessionId = StorageService.getSessionId();
    
    if (!user || !sessionId) {
      return false;
    }

    // Validate session
    return this.sessionModel.validateSession(sessionId);
  }

  /**
   * Get current user
   * @returns {object|null} current user or null
   */
  getCurrentUser() {
    if (this.isAuthenticated()) {
      return StorageService.getCurrentUser();
    }
    return null;
  }

  /**
   * Check if current user has admin role
   * @returns {boolean} true if user is admin
   */
  isAdmin() {
    const user = this.getCurrentUser();
    return user && user.role === USER_ROLES.ADMIN;
  }

  /**
   * Check if current user has specific role
   * @param {string} role 
   * @returns {boolean} true if user has the role
   */
  hasRole(role) {
    const user = this.getCurrentUser();
    return user && user.role === role;
  }

  /**
   * Refresh session
   * @returns {boolean} true if session refreshed successfully
   */
  refreshSession() {
    const sessionId = StorageService.getSessionId();
    
    if (sessionId) {
      return this.sessionModel.updateSessionActivity(sessionId);
    }
    
    return false;
  }

  /**
   * Change password
   * @param {string} currentPassword 
   * @param {string} newPassword 
   * @returns {Promise<object>} change password result
   */
  async changePassword(currentPassword, newPassword) {
    try {
      const user = this.getCurrentUser();
      
      if (!user) {
        return {
          success: false,
          message: 'User not authenticated'
        };
      }

      // Get user with password to verify current password
      const userWithPassword = this.userModel.getUserById(user.id);
      
      if (!userWithPassword || userWithPassword.password !== currentPassword) {
        return {
          success: false,
          message: 'Current password is incorrect'
        };
      }

      // Update password
      const updatedUser = this.userModel.updateUser(user.id, { password: newPassword });
      
      if (updatedUser) {
        return {
          success: true,
          message: 'Password changed successfully'
        };
      } else {
        return {
          success: false,
          message: 'Failed to update password'
        };
      }
    } catch (error) {
      console.error('Change password error:', error);
      return {
        success: false,
        message: 'An error occurred while changing password'
      };
    }
  }

  /**
   * Update user profile
   * @param {object} profileData 
   * @returns {Promise<object>} update result
   */
  async updateProfile(profileData) {
    try {
      const user = this.getCurrentUser();
      
      if (!user) {
        return {
          success: false,
          message: 'User not authenticated'
        };
      }

      // Update user profile (excluding password and role)
      const allowedFields = ['firstName', 'lastName', 'email'];
      const updateData = {};
      
      allowedFields.forEach(field => {
        if (profileData[field] !== undefined) {
          updateData[field] = profileData[field];
        }
      });

      const updatedUser = this.userModel.updateUser(user.id, updateData);
      
      if (updatedUser) {
        // Update stored user data
        StorageService.setCurrentUser(updatedUser);
        
        return {
          success: true,
          message: 'Profile updated successfully',
          user: updatedUser
        };
      } else {
        return {
          success: false,
          message: 'Failed to update profile'
        };
      }
    } catch (error) {
      console.error('Update profile error:', error);
      return {
        success: false,
        message: error.message || 'An error occurred while updating profile'
      };
    }
  }
}

export default AuthService;
