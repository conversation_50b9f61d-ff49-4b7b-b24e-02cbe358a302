import { SESSION_STORAGE_KEYS } from '../utils/constants';

/**
 * Storage Service for managing browser storage
 */
class StorageService {
  /**
   * Set item in localStorage
   * @param {string} key 
   * @param {any} value 
   */
  static setItem(key, value) {
    try {
      const serializedValue = JSON.stringify(value);
      localStorage.setItem(key, serializedValue);
    } catch (error) {
      console.error('Error saving to localStorage:', error);
    }
  }

  /**
   * Get item from localStorage
   * @param {string} key 
   * @returns {any} parsed value or null
   */
  static getItem(key) {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Error reading from localStorage:', error);
      return null;
    }
  }

  /**
   * Remove item from localStorage
   * @param {string} key 
   */
  static removeItem(key) {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      console.error('Error removing from localStorage:', error);
    }
  }

  /**
   * Clear all items from localStorage
   */
  static clear() {
    try {
      localStorage.clear();
    } catch (error) {
      console.error('Error clearing localStorage:', error);
    }
  }

  /**
   * Set current user in storage
   * @param {object} user 
   */
  static setCurrentUser(user) {
    this.setItem(SESSION_STORAGE_KEYS.USER, user);
  }

  /**
   * Get current user from storage
   * @returns {object|null} user object or null
   */
  static getCurrentUser() {
    return this.getItem(SESSION_STORAGE_KEYS.USER);
  }

  /**
   * Remove current user from storage
   */
  static removeCurrentUser() {
    this.removeItem(SESSION_STORAGE_KEYS.USER);
  }

  /**
   * Set session ID in storage
   * @param {string} sessionId 
   */
  static setSessionId(sessionId) {
    this.setItem(SESSION_STORAGE_KEYS.SESSION_ID, sessionId);
  }

  /**
   * Get session ID from storage
   * @returns {string|null} session ID or null
   */
  static getSessionId() {
    return this.getItem(SESSION_STORAGE_KEYS.SESSION_ID);
  }

  /**
   * Remove session ID from storage
   */
  static removeSessionId() {
    this.removeItem(SESSION_STORAGE_KEYS.SESSION_ID);
  }

  /**
   * Set auth token in storage
   * @param {string} token 
   */
  static setAuthToken(token) {
    this.setItem(SESSION_STORAGE_KEYS.AUTH_TOKEN, token);
  }

  /**
   * Get auth token from storage
   * @returns {string|null} auth token or null
   */
  static getAuthToken() {
    return this.getItem(SESSION_STORAGE_KEYS.AUTH_TOKEN);
  }

  /**
   * Remove auth token from storage
   */
  static removeAuthToken() {
    this.removeItem(SESSION_STORAGE_KEYS.AUTH_TOKEN);
  }

  /**
   * Clear all authentication data
   */
  static clearAuthData() {
    this.removeCurrentUser();
    this.removeSessionId();
    this.removeAuthToken();
  }

  /**
   * Check if user is logged in
   * @returns {boolean} true if user is logged in
   */
  static isLoggedIn() {
    const user = this.getCurrentUser();
    const sessionId = this.getSessionId();
    return !!(user && sessionId);
  }
}

export default StorageService;
