// Application Constants
export const USER_ROLES = {
  ADMIN: 'admin',
  USER: 'user'
};

export const SESSION_STORAGE_KEYS = {
  USER: 'currentUser',
  SESSION_ID: 'sessionId',
  AUTH_TOKEN: 'authToken'
};

export const ROUTES = {
  HOME: '/',
  LOGIN: '/login',
  ADMIN: '/admin',
  ADMIN_USERS: '/admin/users',
  ADMIN_DASHBOARD: '/admin/dashboard'
};

export const API_ENDPOINTS = {
  LOGIN: '/api/auth/login',
  LOGOUT: '/api/auth/logout',
  USERS: '/api/users',
  SESSIONS: '/api/sessions'
};

export const VALIDATION_RULES = {
  USERNAME: {
    MIN_LENGTH: 3,
    MAX_LENGTH: 20,
    PATTERN: /^[a-zA-Z0-9_]+$/
  },
  PASSWORD: {
    MIN_LENGTH: 6,
    MAX_LENGTH: 50
  },
  EMAIL: {
    PATTERN: /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  }
};

export const MESSAGES = {
  LOGIN_SUCCESS: 'Login successful!',
  LOGIN_FAILED: 'Invalid username or password',
  LOGOUT_SUCCESS: 'Logged out successfully',
  ACCESS_DENIED: 'Access denied. Admin privileges required.',
  SESSION_EXPIRED: 'Session expired. Please login again.',
  USER_CREATED: 'User created successfully',
  USER_UPDATED: 'User updated successfully',
  USER_DELETED: 'User deleted successfully'
};
