import { VALIDATION_RULES } from './constants';

/**
 * Validate username
 * @param {string} username 
 * @returns {object} validation result
 */
export const validateUsername = (username) => {
  const errors = [];
  
  if (!username) {
    errors.push('Username is required');
  } else {
    if (username.length < VALIDATION_RULES.USERNAME.MIN_LENGTH) {
      errors.push(`Username must be at least ${VALIDATION_RULES.USERNAME.MIN_LENGTH} characters`);
    }
    if (username.length > VALIDATION_RULES.USERNAME.MAX_LENGTH) {
      errors.push(`Username must not exceed ${VALIDATION_RULES.USERNAME.MAX_LENGTH} characters`);
    }
    if (!VALIDATION_RULES.USERNAME.PATTERN.test(username)) {
      errors.push('Username can only contain letters, numbers, and underscores');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate password
 * @param {string} password 
 * @returns {object} validation result
 */
export const validatePassword = (password) => {
  const errors = [];
  
  if (!password) {
    errors.push('Password is required');
  } else {
    if (password.length < VALIDATION_RULES.PASSWORD.MIN_LENGTH) {
      errors.push(`Password must be at least ${VALIDATION_RULES.PASSWORD.MIN_LENGTH} characters`);
    }
    if (password.length > VALIDATION_RULES.PASSWORD.MAX_LENGTH) {
      errors.push(`Password must not exceed ${VALIDATION_RULES.PASSWORD.MAX_LENGTH} characters`);
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate email
 * @param {string} email 
 * @returns {object} validation result
 */
export const validateEmail = (email) => {
  const errors = [];
  
  if (!email) {
    errors.push('Email is required');
  } else {
    if (!VALIDATION_RULES.EMAIL.PATTERN.test(email)) {
      errors.push('Please enter a valid email address');
    }
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Validate login form
 * @param {object} formData 
 * @returns {object} validation result
 */
export const validateLoginForm = (formData) => {
  const { username, password } = formData;
  const errors = {};
  
  const usernameValidation = validateUsername(username);
  if (!usernameValidation.isValid) {
    errors.username = usernameValidation.errors;
  }
  
  const passwordValidation = validatePassword(password);
  if (!passwordValidation.isValid) {
    errors.password = passwordValidation.errors;
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Validate user form (for admin user management)
 * @param {object} formData 
 * @returns {object} validation result
 */
export const validateUserForm = (formData) => {
  const { username, password, email, firstName, lastName } = formData;
  const errors = {};
  
  const usernameValidation = validateUsername(username);
  if (!usernameValidation.isValid) {
    errors.username = usernameValidation.errors;
  }
  
  if (password) { // Password is optional for updates
    const passwordValidation = validatePassword(password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.errors;
    }
  }
  
  const emailValidation = validateEmail(email);
  if (!emailValidation.isValid) {
    errors.email = emailValidation.errors;
  }
  
  if (!firstName || firstName.trim().length === 0) {
    errors.firstName = ['First name is required'];
  }
  
  if (!lastName || lastName.trim().length === 0) {
    errors.lastName = ['Last name is required'];
  }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};
